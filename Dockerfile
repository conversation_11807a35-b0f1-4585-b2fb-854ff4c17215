FROM php:8.2-apache

ENV DEBIAN_FRONTEND noninteractive
ARG BASE_TOOLS="apt-utils binutils curl gettext git-core jq net-tools ssh vim wget zip libzip-dev"
ARG RUNTIME_DEPS="\
    libcurl4-openssl-dev \
    libedit-dev \
    libjpeg-dev \
    libjpeg62-turbo-dev \
    libmemcached-dev \
    libpng-dev \
    libsqlite3-dev \
    libssl-dev \
    libxml2-dev \
    xz-utils \
    zlib1g-dev \
    libedit2 \
    libjpeg62 \
    libmemcached11 \
    libmemcachedutil2 \
    libonig-dev \
    libpng-dev \
    libxml2-dev \
    zlib1g \
    libicu-dev \
    libxslt1.1 \
    libxslt-dev \
    apt-transport-https"

ONBUILD ARG COMPOSER_GITLAB_TOKEN
# Persist the value that was used in the image's `env`
ONBUILD ENV COMPOSER_GITLAB_TOKEN=$COMPOSER_GITLAB_TOKEN

RUN apt-get update && \
    apt-get upgrade -y && \
    apt-get install -y --no-install-recommends \
    $BASE_TOOLS \
    $RUNTIME_DEPS && \
    apt-get clean all

RUN a2enmod deflate expires headers rewrite usertrack && \
    a2disconf charset localized-error-pages other-vhosts-access-log serve-cgi-bin

RUN docker-php-ext-configure ftp --with-openssl-dir=/usr && \
    docker-php-ext-install ftp && \
    docker-php-ext-install bcmath && \
    docker-php-ext-install opcache && \
    docker-php-ext-install pdo_mysql && \
    docker-php-ext-install soap && \
    docker-php-ext-install sockets && \
    docker-php-ext-install zip && \
    docker-php-ext-configure intl && \
    docker-php-ext-install intl && \
    docker-php-ext-install xsl
    
RUN apt-get update && apt-get install -y \ 
    git \
    unzip \
    libssl-dev \
    pkg-config \
    libmemcached-dev \
    zlib1g-dev \
    && pecl install opentelemetry-beta \
    && docker-php-ext-enable opentelemetry \
    && pecl install memcached \
    && docker-php-ext-enable memcached \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

RUN docker-php-ext-configure gd --with-jpeg 
RUN docker-php-ext-install -j$(nproc) gd

RUN pecl install xdebug

RUN mkdir -p /var/www/service/var && \
    chown -R www-data /var/www/service && \
    chgrp -R www-data /var/www/service




ENV PORT 81

COPY php/sf_php.ini  $PHP_INI_DIR/conf.d/sf_php.ini
COPY apache/apache2.conf apache/ports.conf /etc/apache2/
COPY apache/service.conf /etc/apache2/sites-enabled/000-default.conf
COPY apache/mpm_prefork.conf /etc/apache2/mods-enabled/mpm_prefork.conf
COPY apache/security.conf /etc/apache2/conf-available/security.conf
COPY apache/deflate.conf apache/expires.conf apache/headers.conf /etc/apache2/mods-available/
COPY docker-php-entrypoint /usr/local/bin/docker-php-entrypoint

WORKDIR /var/www/service
COPY .htaccess /var/www/
COPY auth.json ./

ONBUILD COPY composer.phar composer.json composer.lock symfony.lock ./
ONBUILD RUN php composer.phar config --global gitlab-token.gitlab.com $COMPOSER_GITLAB_TOKEN

ONBUILD RUN php composer.phar install --no-scripts --no-autoloader
ONBUILD COPY . ./
ONBUILD RUN php composer.phar dump-autoload --optimize --no-dev --classmap-authoritative
