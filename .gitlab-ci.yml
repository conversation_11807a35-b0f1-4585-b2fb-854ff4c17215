variables:
  CI_SCRIPTS_DIR: "/ci_tools/scripts"
  IMAGE: "850077434821.dkr.ecr.us-east-1.amazonaws.com/sparefoot/symfony5_php8_grafana"

stages:
  - build

################################################################################
#
# BUILD
#
################################################################################

build_and_push_dev:
  stage: build
  tags:
    - sf-php8
  except:
    - master
    - release
  variables:
    SF_ENV: "dev"
  script:
    - ${CI_SCRIPTS_DIR}/authorize_composer.sh
    - ${CI_SCRIPTS_DIR}/build_image.sh
    - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 850077434821.dkr.ecr.us-east-1.amazonaws.com
    - docker push ${IMAGE}:${SF_ENV}
    - docker push ${IMAGE}:${CI_PIPELINE_ID}

build_and_push_stage:
  stage: build
  tags:
    - sf-php8
  only:
    - master
  variables:
    SF_ENV: "stage"
  script:
    - ${CI_SCRIPTS_DIR}/authorize_composer.sh
    - ${CI_SCRIPTS_DIR}/build_image.sh
    - aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 850077434821.dkr.ecr.us-east-1.amazonaws.com
    - echo "[*] IMAGE ${IMAGE}, ENV ${SF_ENV}, PIPELINEID ${CI_PIPELINE_ID}"
    - docker push ${IMAGE}:${SF_ENV}
    - docker push ${IMAGE}:${CI_PIPELINE_ID}

