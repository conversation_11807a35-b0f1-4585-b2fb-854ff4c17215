always_populate_raw_post_data = -1
error_log = /var/log/php.log
error_reporting = E_ERROR | E_WARNING | E_PARSE
expose_php = Off
short_open_tag = On
variables_order = "EGPCS"
realpath_cache_size=4096K
realpath_cache_ttl=600
output_buffering = 4096
upload_max_filesize = 25M
post_max_size = 26M
max_execution_time = 300 ; Maximum execution time of each script, in seconds
max_input_time = 300     ; Maximum amount of time each script may spend parsing request data
memory_limit = 1024M     ; Maximum amount of memory a script may consume (default: 128M)

[Date]
; Defines the default timezone used by the date functions
date.timezone = America/Chicago

[opcache]
; http://php.net/manual/en/opcache.configuration.php
opcache.enable=1
opcache.memory_consumption=256
opcache.max_accelerated_files=20000
; expect files not to change more often than this
; set to zero to make opcache check on every request
opcache.revalidate_freq=600
opcache.validate_timestamps=0
; override in your project ^ to enable revalidate_freq to check timestamps for changes
; sets the second level cache directory, for when SHM is full
opcache.file_cache=/tmp/opcache

[xdebug]
xdebug.overload_var_dump=0

; https://www.scalingphpbook.com/blog/2014/02/14/best-zend-opcache-settings.html
opcache.interned_strings_buffer=16
opcache.fast_shutdown=1

; leave newline at end for extending images to concat onto