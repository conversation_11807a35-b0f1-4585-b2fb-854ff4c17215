#!/bin/sh
set -e

# first arg is `-f` or `--some-option`
if [ "${1#-}" != "$1" ]; then
	set -- php "$@"
fi

envsubst < /etc/apache2/ports.conf > /etc/apache2/ports_tmp.conf 
mv /etc/apache2/ports_tmp.conf /etc/apache2/ports.conf

envsubst < /etc/apache2/sites-enabled/000-default.conf > /etc/apache2/sites-enabled/000-default-tmp.conf
mv /etc/apache2/sites-enabled/000-default-tmp.conf /etc/apache2/sites-enabled/000-default.conf

if [ "$SF_ENV" = "local" ]; then
    echo "disabling opcache!!!!!!!"
    export DD_TRACE_ENABLED=false
    echo "opcache.enable=0" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "zend_extension=$(find /usr/local/lib/php/extensions/ -name xdebug.so)" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "xdebug.remote_enable=1" >> /usr/local/etc/php/conf.d/sf_php.ini
    echo "xdebug.remote_port=9093" >> /usr/local/etc/php/conf.d/sf_php.ini
elif [ "$SF_ENV" = "prod" ]; then 
    echo "enable preloading"
    PRELOAD=`ls /var/www/service/var/cache/prod/*KernelProdContainer.preload.php | head -n1`
    echo "opcache.preload=$PRELOAD" >> /usr/local/etc/php/conf.d/sf_php.ini
fi

echo "extension=opentelemetry.so" >> /usr/local/etc/php/conf.d/sf_php.ini

echo "extension=memcached" >> /usr/local/etc/php/conf.d/sf_php.ini

export APP_ENV=$SF_ENV
echo "APP_ENV=${APP_ENV}" > .env.$SF_ENV.local

# warm up symfony cache regardless of environment
if [ -f "bin/console" ]
then
    bin/console cache:warmup
fi

chown -R www-data /var/www/service/var
chgrp -R www-data /var/www/service/var

exec "$@"
